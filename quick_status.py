#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速状态检查工具
"""

import os
import json
from datetime import datetime

def check_status():
    """检查爬虫状态"""
    data_dir = "books_data_advanced"
    
    print("🔍 清华大学出版社图书爬虫 - 快速状态检查")
    print("=" * 50)
    print(f"📅 检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 检查发现的链接
    links_file = os.path.join(data_dir, "discovered_links.json")
    if os.path.exists(links_file):
        with open(links_file, 'r', encoding='utf-8') as f:
            links_data = json.load(f)
        print(f"🔗 发现链接: {links_data.get('count', 0)} 个")
        print(f"🕐 最后更新: {links_data.get('last_update', '未知')}")
    else:
        print("❌ 未找到链接数据文件")
    
    # 检查图书数据
    books_file = os.path.join(data_dir, "books_comprehensive.json")
    if os.path.exists(books_file):
        with open(books_file, 'r', encoding='utf-8') as f:
            books_data = json.load(f)
        successful_books = [b for b in books_data if b.get('success', False)]
        print(f"📚 已爬取图书: {len(books_data)} 本")
        print(f"✅ 成功提取: {len(successful_books)} 本")
        if len(books_data) > 0:
            success_rate = len(successful_books) / len(books_data) * 100
            print(f"📈 成功率: {success_rate:.1f}%")
    else:
        print("❌ 未找到图书数据文件")
    
    # 检查统计数据
    stats_file = os.path.join(data_dir, "crawl_statistics.json")
    if os.path.exists(stats_file):
        with open(stats_file, 'r', encoding='utf-8') as f:
            stats = json.load(f)
        print(f"🌐 总请求数: {stats.get('total_requests', 0)}")
        print(f"✅ 成功请求: {stats.get('successful_requests', 0)}")
        print(f"❌ 失败请求: {stats.get('failed_requests', 0)}")
    else:
        print("❌ 未找到统计数据文件")
    
    # 检查图片
    images_dir = os.path.join(data_dir, "images")
    if os.path.exists(images_dir):
        image_files = [f for f in os.listdir(images_dir) if f.endswith(('.jpg', '.png', '.gif'))]
        print(f"🖼️  下载图片: {len(image_files)} 张")
    else:
        print("❌ 未找到图片目录")
    
    # 检查日志
    logs_dir = os.path.join(data_dir, "logs")
    if os.path.exists(logs_dir):
        log_files = [f for f in os.listdir(logs_dir) if f.endswith('.log')]
        print(f"📝 日志文件: {len(log_files)} 个")
        
        # 检查最新日志
        latest_log = None
        latest_time = 0
        for log_file in log_files:
            if log_file.startswith('crawler_'):
                log_path = os.path.join(logs_dir, log_file)
                mtime = os.path.getmtime(log_path)
                if mtime > latest_time:
                    latest_time = mtime
                    latest_log = log_file
        
        if latest_log:
            print(f"📄 最新日志: {latest_log}")
            log_path = os.path.join(logs_dir, latest_log)
            try:
                with open(log_path, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                if lines:
                    print("📋 最新日志内容 (最后5行):")
                    for line in lines[-5:]:
                        print(f"   {line.strip()}")
            except:
                print("❌ 无法读取日志文件")
    else:
        print("❌ 未找到日志目录")
    
    print("=" * 50)

if __name__ == "__main__":
    check_status()
