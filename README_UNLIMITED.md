# 清华大学出版社图书信息爬虫 - 深度优化无限制版本

🚀 **专业级图书信息爬虫，支持一次性爬取所有图书，无数量限制！**

这是一个深度优化的Python爬虫程序，专门用于爬取清华大学出版社的所有图书信息。采用多种搜索策略和智能算法，确保最大化的信息收集效率。

## ✨ 核心特性

### 🎯 无限制深度爬取
- **无数量限制** - 一次性爬取网站上所有可发现的图书
- **8种搜索策略** - 多维度发现图书链接，确保覆盖全面
- **智能去重机制** - 基于ISBN和URL的双重去重
- **断点续传** - 支持中断恢复，大规模爬取无忧

### ⚡ 高性能优化
- **多线程并发** - 支持1-16线程，可根据需求调整
- **连接池管理** - 复用HTTP连接，提升效率
- **智能延迟策略** - 根据成功率动态调整请求间隔
- **内存优化** - 流式处理，支持大规模数据爬取

### 🔍 深度信息提取
- **增强正则匹配** - 多种模式匹配，提高信息提取准确率
- **结构化数据解析** - 支持JSON-LD和Meta标签数据提取
- **完整图书信息** - 标题、作者、价格、ISBN、出版日期、页数、分类、描述等
- **高质量图片下载** - 自动下载并验证封面图片

### 📊 实时监控与分析
- **实时监控面板** - 查看爬取进度和性能指标
- **详细统计分析** - 成功率、速度、图书分析等
- **历史数据记录** - 支持趋势分析和性能优化
- **多格式导出** - JSON、CSV格式，支持数据分析

## 🚀 快速开始

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 无限制深度爬取
```bash
# 使用专门的无限制爬虫
python unlimited_crawler.py
```

### 3. 实时监控
```bash
# 在另一个终端窗口运行监控
python enhanced_monitor.py
```

## 📁 项目结构

```
Textbook_Library_crawler/
├── advanced_tup_crawler.py    # 核心爬虫引擎
├── unlimited_crawler.py       # 无限制爬取启动器
├── enhanced_monitor.py        # 深度监控工具
├── requirements.txt           # 依赖包列表
├── books_data_advanced/       # 输出目录
│   ├── books_comprehensive.json    # 完整图书数据(JSON)
│   ├── books_comprehensive.csv     # 完整图书数据(CSV)
│   ├── crawl_statistics.json       # 爬取统计信息
│   ├── discovered_links.json       # 发现的链接
│   ├── processed_state.json        # 处理状态(断点续传)
│   ├── progress_report.txt          # 详细进度报告
│   ├── images/                      # 图书封面图片
│   └── logs/                        # 详细日志文件
└── README.md                  # 项目说明
```

## 🎛️ 配置选项

### 爬取模式
1. **保守模式** - 单线程，延迟较长，最稳定
2. **平衡模式** - 4线程，推荐日常使用
3. **高速模式** - 8线程，最大性能
4. **自定义模式** - 自定义线程数和参数

### 监控选项
1. **实时状态查看** - 查看当前爬取状态
2. **持续监控** - 10秒/30秒/60秒自动刷新
3. **历史数据分析** - 查看爬取趋势和性能

## 📊 输出数据格式

### JSON格式示例
```json
{
  "title": "深度学习入门：基于Python的理论与实现",
  "author": "斋藤康毅",
  "price": "59.00元",
  "isbn": "9787302461906",
  "publish_date": "2018.07.01",
  "publisher": "清华大学出版社",
  "pages": "285页",
  "category": "计算机科学",
  "description": "本书是深度学习真正意义上的入门书...",
  "cover_image_file": "9787302461906_深度学习入门.jpg",
  "url": "https://www.tup.com.cn/booksCenter/book_xxx.html",
  "crawl_time": "2025-06-10T15:30:45.123456",
  "success": true
}
```

## 🔧 高级功能

### 搜索策略
1. **首页搜索** - 从网站首页发现链接
2. **图书中心搜索** - 从图书中心页面搜索
3. **分类深度搜索** - 遍历所有分类和年份
4. **分页系统搜索** - 自动翻页获取更多链接
5. **最新图书搜索** - 获取最新发布的图书
6. **热门图书搜索** - 获取热门推荐图书
7. **年份范围搜索** - 按年份范围系统搜索
8. **站点地图搜索** - 从sitemap获取链接

### 数据质量保证
- **多重验证** - 标题、ISBN、价格等关键信息验证
- **图片质量检查** - 自动验证下载图片的完整性
- **数据完整性检查** - 确保每条记录的数据质量
- **错误恢复机制** - 自动重试和错误处理

## 📈 性能指标

在标准配置下的预期性能：
- **爬取速度**: 20-50本图书/分钟
- **请求成功率**: >95%
- **数据提取成功率**: >90%
- **图片下载成功率**: >85%

## ⚠️ 重要提示

### 使用建议
- 🕐 **运行时间**: 完整爬取可能需要数小时，建议在网络稳定时运行
- 🔄 **断点续传**: 支持随时中断(Ctrl+C)和恢复，数据自动保存
- 📊 **实时监控**: 建议同时运行监控工具查看进度
- 💾 **存储空间**: 确保有足够磁盘空间存储图书数据和图片

### 合规使用
- 📋 **遵守robots.txt** - 自动检查并遵守网站爬取规则
- ⏱️ **合理延迟** - 内置智能延迟机制，避免对服务器造成压力
- 🎯 **学术用途** - 建议仅用于学术研究和个人学习
- 📚 **数据使用** - 请尊重版权，合理使用爬取的数据

## 🛠️ 故障排除

### 常见问题
1. **网络连接问题** - 检查网络连接，程序会自动重试
2. **权限问题** - 确保有写入输出目录的权限
3. **内存不足** - 降低线程数或重启程序
4. **磁盘空间不足** - 清理空间或更改输出目录

### 日志分析
- 查看 `logs/crawler_*.log` 了解详细运行情况
- 查看 `logs/errors.log` 分析错误信息
- 查看 `logs/performance.log` 分析性能数据

## 📞 技术支持

如遇到问题，请查看：
1. 详细日志文件
2. 进度报告文件
3. 统计数据文件

## 📄 许可证

MIT License - 详见 LICENSE 文件

---

🎉 **现在您可以使用这个深度优化的爬虫来一次性获取清华大学出版社的所有图书信息！**
