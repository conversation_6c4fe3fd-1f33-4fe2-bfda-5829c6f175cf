#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
清华大学出版社图书信息爬虫 - 无限制深度优化版本
专门用于一次性爬取所有图书信息，不设数量限制
"""

import os
import sys
import time
from datetime import datetime
from advanced_tup_crawler import AdvancedTupCrawler

def print_banner():
    """打印启动横幅"""
    print("🌟" * 30)
    print("🚀 清华大学出版社图书爬虫")
    print("📚 深度优化无限制版本 v3.0")
    print("🎯 目标: 爬取所有可发现的图书信息")
    print("🌟" * 30)
    print()

def print_system_info():
    """打印系统信息"""
    print("📋 系统信息:")
    print(f"  - Python版本: {sys.version.split()[0]}")
    print(f"  - 当前时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"  - 工作目录: {os.getcwd()}")
    print()

def get_user_config():
    """获取用户配置"""
    print("⚙️ 配置选项:")
    print("1. 🐌 保守模式 (单线程, 延迟较长, 最稳定)")
    print("2. ⚡ 平衡模式 (4线程, 推荐)")
    print("3. 🚀 高速模式 (8线程, 最快速度)")
    print("4. 🔧 自定义配置")
    
    while True:
        choice = input("\n请选择配置 (1-4): ").strip()
        
        if choice == "1":
            return {"max_workers": 1, "enable_threading": False, "mode": "保守模式"}
        elif choice == "2":
            return {"max_workers": 4, "enable_threading": True, "mode": "平衡模式"}
        elif choice == "3":
            return {"max_workers": 8, "enable_threading": True, "mode": "高速模式"}
        elif choice == "4":
            return get_custom_config()
        else:
            print("❌ 无效选择，请重新输入")

def get_custom_config():
    """获取自定义配置"""
    print("\n🔧 自定义配置:")
    
    while True:
        try:
            threading_choice = input("使用多线程? (y/n): ").lower()
            enable_threading = threading_choice in ['y', 'yes', '是']
            
            if enable_threading:
                max_workers = int(input("线程数量 (1-16): "))
                max_workers = max(1, min(16, max_workers))
            else:
                max_workers = 1
            
            return {
                "max_workers": max_workers, 
                "enable_threading": enable_threading,
                "mode": f"自定义({max_workers}线程)" if enable_threading else "自定义(单线程)"
            }
        except ValueError:
            print("❌ 输入无效，请重新输入")

def confirm_start(config):
    """确认开始爬取"""
    print(f"\n📋 配置确认:")
    print(f"  - 爬取模式: {config['mode']}")
    print(f"  - 多线程: {'是' if config['enable_threading'] else '否'}")
    print(f"  - 线程数: {config['max_workers']}")
    print(f"  - 数量限制: 无 (爬取所有可发现的图书)")
    print(f"  - 输出目录: books_data_advanced/")
    print(f"  - 预期时间: 根据网站图书数量而定 (可能需要数小时)")
    
    print("\n⚠️ 重要提示:")
    print("  - 此模式将尝试爬取网站上所有图书信息")
    print("  - 爬取过程可能需要较长时间")
    print("  - 程序支持中断恢复，可随时按Ctrl+C安全停止")
    print("  - 所有数据将自动保存，支持断点续传")
    
    while True:
        confirm = input("\n确认开始无限制爬取? (y/n): ").lower()
        if confirm in ['y', 'yes', '是']:
            return True
        elif confirm in ['n', 'no', '否']:
            return False
        else:
            print("❌ 请输入 y 或 n")

def run_unlimited_crawl():
    """运行无限制爬取"""
    print_banner()
    print_system_info()
    
    # 获取配置
    config = get_user_config()
    
    # 确认开始
    if not confirm_start(config):
        print("❌ 取消爬取")
        return
    
    print("\n🚀 开始无限制深度爬取...")
    print("💡 提示: 按 Ctrl+C 可安全停止程序")
    print("-" * 50)
    
    # 创建爬虫实例
    crawler = AdvancedTupCrawler(
        max_workers=config['max_workers'],
        enable_threading=config['enable_threading']
    )
    
    start_time = time.time()
    
    try:
        # 开始爬取
        crawler.crawl_books_advanced(use_threading=config['enable_threading'])
        
        # 计算总耗时
        total_time = time.time() - start_time
        
        print("\n🎉 爬取完成!")
        print(f"⏱️ 总耗时: {total_time/60:.1f} 分钟")
        
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断程序")
        print("💾 正在保存已爬取的数据...")
        crawler.save_comprehensive_data()
        
        total_time = time.time() - start_time
        print(f"⏱️ 运行时间: {total_time/60:.1f} 分钟")
        print("✅ 数据已安全保存，可稍后继续爬取")
        
    except Exception as e:
        print(f"\n❌ 程序执行出错: {str(e)}")
        print("💾 正在保存已爬取的数据...")
        crawler.save_comprehensive_data()
        
    finally:
        print("\n📊 查看结果:")
        print("  - books_data_advanced/books_comprehensive.json")
        print("  - books_data_advanced/books_comprehensive.csv")
        print("  - books_data_advanced/crawl_statistics.json")
        print("  - books_data_advanced/progress_report.txt")

def main():
    """主函数"""
    try:
        run_unlimited_crawl()
    except Exception as e:
        print(f"\n💥 程序启动失败: {str(e)}")
        print("请检查依赖是否正确安装")

if __name__ == "__main__":
    main()
