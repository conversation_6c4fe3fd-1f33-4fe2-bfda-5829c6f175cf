#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
清华大学出版社图书爬虫监控工具 - 深度优化版本
实时监控爬虫运行状态和进度，支持无限制爬取监控
"""

import os
import json
import time
import csv
from datetime import datetime, timedelta
from typing import Dict, Any, List
from collections import defaultdict

class EnhancedCrawlerMonitor:
    def __init__(self, data_dir="books_data_advanced"):
        self.data_dir = data_dir
        self.stats_file = os.path.join(data_dir, "crawl_statistics.json")
        self.books_file = os.path.join(data_dir, "books_comprehensive.json")
        self.state_file = os.path.join(data_dir, "processed_state.json")
        self.progress_file = os.path.join(data_dir, "progress_report.txt")
        self.links_file = os.path.join(data_dir, "discovered_links.json")
        
        # 历史数据记录
        self.history_file = os.path.join(data_dir, "monitor_history.json")
        self.load_history()
        
    def load_history(self):
        """加载历史监控数据"""
        try:
            if os.path.exists(self.history_file):
                with open(self.history_file, 'r', encoding='utf-8') as f:
                    self.history = json.load(f)
            else:
                self.history = []
        except Exception:
            self.history = []
    
    def save_history(self, data):
        """保存历史监控数据"""
        try:
            self.history.append({
                'timestamp': datetime.now().isoformat(),
                'data': data
            })
            
            # 只保留最近1000条记录
            if len(self.history) > 1000:
                self.history = self.history[-1000:]
            
            with open(self.history_file, 'w', encoding='utf-8') as f:
                json.dump(self.history, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存历史数据失败: {e}")
        
    def load_stats(self) -> Dict[str, Any]:
        """加载统计数据"""
        try:
            if os.path.exists(self.stats_file):
                with open(self.stats_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            print(f"加载统计数据失败: {e}")
        return {}
    
    def load_books_data(self) -> list:
        """加载图书数据"""
        try:
            if os.path.exists(self.books_file):
                with open(self.books_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            print(f"加载图书数据失败: {e}")
        return []
    
    def load_state(self) -> Dict[str, Any]:
        """加载处理状态"""
        try:
            if os.path.exists(self.state_file):
                with open(self.state_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            print(f"加载状态数据失败: {e}")
        return {}
    
    def load_discovered_links(self) -> Dict[str, Any]:
        """加载发现的链接数据"""
        try:
            if os.path.exists(self.links_file):
                with open(self.links_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            print(f"加载链接数据失败: {e}")
        return {}
    
    def calculate_speed_metrics(self, stats: Dict[str, Any]) -> Dict[str, float]:
        """计算速度指标"""
        metrics = {}
        
        start_time_str = stats.get('start_time')
        if start_time_str:
            try:
                start_time = datetime.fromisoformat(start_time_str.replace('Z', '+00:00'))
                current_time = datetime.now()
                duration = (current_time - start_time).total_seconds()
                
                if duration > 0:
                    metrics['requests_per_minute'] = stats.get('total_requests', 0) / (duration / 60)
                    metrics['books_per_minute'] = stats.get('books_found', 0) / (duration / 60)
                    metrics['duration_minutes'] = duration / 60
                    
            except Exception:
                pass
                
        return metrics
    
    def print_enhanced_status(self):
        """打印增强的状态信息"""
        stats = self.load_stats()
        books = self.load_books_data()
        state = self.load_state()
        links_data = self.load_discovered_links()
        
        print("\n" + "🌟"*30)
        print("🔍 清华大学出版社图书爬虫 - 深度监控面板")
        print("🌟"*30)
        print(f"📅 监控时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 基础统计
        if stats:
            print(f"\n📊 爬取统计:")
            print(f"  🌐 总请求数: {stats.get('total_requests', 0):,}")
            print(f"  ✅ 成功请求: {stats.get('successful_requests', 0):,}")
            print(f"  ❌ 失败请求: {stats.get('failed_requests', 0):,}")
            
            if stats.get('total_requests', 0) > 0:
                success_rate = stats.get('successful_requests', 0) / stats.get('total_requests', 1) * 100
                print(f"  📈 请求成功率: {success_rate:.1f}%")
            
            # 速度指标
            speed_metrics = self.calculate_speed_metrics(stats)
            if speed_metrics:
                print(f"\n⚡ 性能指标:")
                print(f"  🕐 运行时间: {speed_metrics.get('duration_minutes', 0):.1f} 分钟")
                print(f"  🚀 请求速度: {speed_metrics.get('requests_per_minute', 0):.1f} 请求/分钟")
                print(f"  📚 图书速度: {speed_metrics.get('books_per_minute', 0):.1f} 本/分钟")
        
        # 链接发现情况
        if links_data:
            print(f"\n🔗 链接发现:")
            print(f"  🎯 发现链接: {links_data.get('count', 0):,}")
            last_update = links_data.get('last_update', '')
            if last_update:
                print(f"  🕐 最后更新: {last_update}")
        
        # 图书信息详细统计
        if books:
            print(f"\n📚 图书信息详细:")
            print(f"  📖 总发现数: {len(books):,}")
            
            successful_books = [b for b in books if b.get('success', False)]
            failed_books = [b for b in books if not b.get('success', False)]
            
            print(f"  ✅ 成功提取: {len(successful_books):,}")
            print(f"  ❌ 提取失败: {len(failed_books):,}")
            
            if len(books) > 0:
                book_success_rate = len(successful_books) / len(books) * 100
                print(f"  📈 提取成功率: {book_success_rate:.1f}%")
            
            # 统计分析
            if successful_books:
                self.print_book_analytics(successful_books)
            
            # 显示最新的几本书
            if successful_books:
                print(f"\n📚 最新成功的图书 (最近10本):")
                for i, book in enumerate(successful_books[-10:], 1):
                    title = book.get('title', '未知标题')[:40]
                    author = book.get('author', '未知作者')[:20]
                    price = book.get('price', '未知价格')
                    isbn = book.get('isbn', '无ISBN')
                    print(f"  {i:2d}. {title} | {author} | {price} | {isbn}")
        
        # 处理状态
        if state:
            print(f"\n🔄 处理状态:")
            processed_urls = len(state.get('processed_urls', []))
            processed_isbns = len(state.get('processed_isbns', []))
            print(f"  🔗 已处理URL: {processed_urls:,}")
            print(f"  📚 唯一ISBN: {processed_isbns:,}")
            
            last_update = state.get('last_update', '')
            if last_update:
                print(f"  🕐 状态更新: {last_update}")
        
        # 文件系统状态
        self.print_file_system_status()
        
        # 保存当前状态到历史
        current_data = {
            'stats': stats,
            'books_count': len(books),
            'successful_books': len([b for b in books if b.get('success', False)]),
            'links_count': links_data.get('count', 0) if links_data else 0
        }
        self.save_history(current_data)
        
        print("🌟"*30)
    
    def print_book_analytics(self, books: List[Dict]):
        """打印图书分析统计"""
        print(f"\n📊 图书分析:")
        
        # 价格分析
        prices = []
        for book in books:
            price_str = book.get('price', '')
            if price_str:
                try:
                    price_num = float(price_str.replace('元', '').replace('￥', ''))
                    prices.append(price_num)
                except:
                    pass
        
        if prices:
            avg_price = sum(prices) / len(prices)
            min_price = min(prices)
            max_price = max(prices)
            print(f"  💰 价格统计: 平均 {avg_price:.1f}元, 最低 {min_price:.1f}元, 最高 {max_price:.1f}元")
        
        # 作者统计
        authors = defaultdict(int)
        for book in books:
            author = book.get('author', '').strip()
            if author and author != '未知作者':
                authors[author] += 1
        
        if authors:
            top_authors = sorted(authors.items(), key=lambda x: x[1], reverse=True)[:5]
            print(f"  👨‍💼 高产作者: {', '.join([f'{author}({count}本)' for author, count in top_authors])}")
        
        # 出版年份统计
        years = defaultdict(int)
        for book in books:
            date_str = book.get('publish_date', '')
            if date_str:
                try:
                    year = date_str[:4]
                    if year.isdigit():
                        years[year] += 1
                except:
                    pass
        
        if years:
            recent_years = sorted(years.items(), key=lambda x: x[0], reverse=True)[:3]
            print(f"  📅 近期出版: {', '.join([f'{year}年({count}本)' for year, count in recent_years])}")
    
    def print_file_system_status(self):
        """打印文件系统状态"""
        print(f"\n💾 文件系统状态:")
        
        # 检查主要文件
        files_to_check = [
            ('books_comprehensive.json', '图书数据'),
            ('books_comprehensive.csv', 'CSV数据'),
            ('crawl_statistics.json', '统计数据'),
            ('processed_state.json', '状态数据'),
            ('discovered_links.json', '链接数据'),
            ('progress_report.txt', '进度报告')
        ]
        
        for filename, description in files_to_check:
            filepath = os.path.join(self.data_dir, filename)
            if os.path.exists(filepath):
                size = os.path.getsize(filepath)
                size_mb = size / (1024 * 1024)
                mtime = datetime.fromtimestamp(os.path.getmtime(filepath))
                print(f"  ✅ {description}: {size_mb:.1f}MB (更新: {mtime.strftime('%H:%M:%S')})")
            else:
                print(f"  ❌ {description}: 文件不存在")
        
        # 检查图片目录
        images_dir = os.path.join(self.data_dir, "images")
        if os.path.exists(images_dir):
            image_files = [f for f in os.listdir(images_dir) if f.endswith(('.jpg', '.png', '.gif'))]
            total_size = sum(os.path.getsize(os.path.join(images_dir, f)) for f in image_files)
            size_mb = total_size / (1024 * 1024)
            print(f"  🖼️  图片文件: {len(image_files)} 个文件, {size_mb:.1f}MB")
        
        # 检查日志目录
        logs_dir = os.path.join(self.data_dir, "logs")
        if os.path.exists(logs_dir):
            log_files = [f for f in os.listdir(logs_dir) if f.endswith('.log')]
            print(f"  📝 日志文件: {len(log_files)} 个文件")
    
    def monitor_continuous(self, interval=10):
        """持续监控"""
        print("🔍 开始深度持续监控 (按 Ctrl+C 停止)")
        print(f"📊 刷新间隔: {interval} 秒")
        
        try:
            while True:
                os.system('clear' if os.name == 'posix' else 'cls')  # 清屏
                self.print_enhanced_status()
                time.sleep(interval)
        except KeyboardInterrupt:
            print("\n⚠️ 监控已停止")

def main():
    """主函数"""
    monitor = EnhancedCrawlerMonitor()
    
    print("🔍 清华大学出版社图书爬虫 - 深度监控工具")
    print("1. 查看当前详细状态")
    print("2. 持续监控 (10秒刷新)")
    print("3. 持续监控 (30秒刷新)")
    print("4. 持续监控 (60秒刷新)")
    
    choice = input("\n请选择 (1-4): ").strip()
    
    if choice == "1":
        monitor.print_enhanced_status()
    elif choice == "2":
        monitor.monitor_continuous(10)
    elif choice == "3":
        monitor.monitor_continuous(30)
    elif choice == "4":
        monitor.monitor_continuous(60)
    else:
        print("无效选择")

if __name__ == "__main__":
    main()
